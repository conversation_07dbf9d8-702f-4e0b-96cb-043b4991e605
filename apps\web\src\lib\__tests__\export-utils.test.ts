import { describe, it, expect, beforeEach, vi } from 'vitest'
import { exportVideo } from '../export-utils'
import { createMockTimelineTrack, createMockTimelineElement, createMockMediaItem } from '@/test/utils'

// Mock FFmpeg
const mockFFmpeg = {
  load: vi.fn(),
  writeFile: vi.fn(),
  readFile: vi.fn().mockResolvedValue(new Uint8Array([1, 2, 3, 4])),
  exec: vi.fn(),
  deleteFile: vi.fn(),
  on: vi.fn(),
}

vi.mock('../ffmpeg-utils', () => ({
  initFFmpeg: vi.fn().mockResolvedValue(mockFFmpeg),
}))

describe('Export Utils', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('exportVideo', () => {
    it('should export video with basic settings', async () => {
      const tracks = [
        createMockTimelineTrack({
          type: 'media',
          elements: [
            createMockTimelineElement({
              type: 'media',
              mediaId: 'test-media-1',
              duration: 10,
              startTime: 0,
            }),
          ],
        }),
      ]

      const mediaItems = [
        createMockMediaItem({
          id: 'test-media-1',
          name: 'test-video.mp4',
          type: 'video',
          duration: 10,
        }),
      ]

      const settings = {
        format: 'mp4' as const,
        quality: '1080p' as const,
        fps: 30,
        bitrate: 'auto' as const,
        audioCodec: 'aac' as const,
        videoCodec: 'h264' as const,
      }

      const progressCallback = vi.fn()

      const result = await exportVideo({
        tracks,
        mediaItems,
        settings,
        totalDuration: 10,
        onProgress: progressCallback,
      })

      expect(result).toBeInstanceOf(Blob)
      expect(result.type).toBe('video/mp4')
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          phase: 'complete',
          progress: 100,
        })
      )
    })

    it('should handle empty timeline', async () => {
      const tracks: any[] = []
      const mediaItems: any[] = []

      const settings = {
        format: 'mp4' as const,
        quality: '1080p' as const,
        fps: 30,
        bitrate: 'auto' as const,
        audioCodec: 'aac' as const,
        videoCodec: 'h264' as const,
      }

      const result = await exportVideo({
        tracks,
        mediaItems,
        settings,
        totalDuration: 10,
      })

      expect(result).toBeInstanceOf(Blob)
      expect(mockFFmpeg.exec).toHaveBeenCalled()
    })

    it('should call progress callback with correct phases', async () => {
      const tracks = [
        createMockTimelineTrack({
          type: 'media',
          elements: [
            createMockTimelineElement({
              type: 'media',
              mediaId: 'test-media-1',
            }),
          ],
        }),
      ]

      const mediaItems = [
        createMockMediaItem({
          id: 'test-media-1',
        }),
      ]

      const settings = {
        format: 'mp4' as const,
        quality: '720p' as const,
        fps: 24,
        bitrate: 'medium' as const,
        audioCodec: 'aac' as const,
        videoCodec: 'h264' as const,
      }

      const progressCallback = vi.fn()

      await exportVideo({
        tracks,
        mediaItems,
        settings,
        totalDuration: 5,
        onProgress: progressCallback,
      })

      // Check that all phases were called
      const calls = progressCallback.mock.calls.map(call => call[0])
      const phases = calls.map(call => call.phase)

      expect(phases).toContain('preparing')
      expect(phases).toContain('processing')
      expect(phases).toContain('finalizing')
      expect(phases).toContain('complete')
    })

    it('should handle custom quality settings', async () => {
      const tracks = [
        createMockTimelineTrack({
          type: 'media',
          elements: [
            createMockTimelineElement({
              type: 'media',
              mediaId: 'test-media-1',
            }),
          ],
        }),
      ]

      const mediaItems = [
        createMockMediaItem({
          id: 'test-media-1',
        }),
      ]

      const settings = {
        format: 'webm' as const,
        quality: 'custom' as const,
        customWidth: 1280,
        customHeight: 720,
        fps: 60,
        bitrate: 'custom' as const,
        customBitrate: 8000,
        audioCodec: 'opus' as const,
        videoCodec: 'vp9' as const,
      }

      const result = await exportVideo({
        tracks,
        mediaItems,
        settings,
        totalDuration: 5,
      })

      expect(result).toBeInstanceOf(Blob)
      expect(result.type).toBe('video/webm')
    })

    it('should handle multiple tracks', async () => {
      const tracks = [
        createMockTimelineTrack({
          type: 'media',
          elements: [
            createMockTimelineElement({
              type: 'media',
              mediaId: 'test-media-1',
              startTime: 0,
              duration: 5,
            }),
          ],
        }),
        createMockTimelineTrack({
          type: 'media',
          elements: [
            createMockTimelineElement({
              type: 'media',
              mediaId: 'test-media-2',
              startTime: 5,
              duration: 5,
            }),
          ],
        }),
      ]

      const mediaItems = [
        createMockMediaItem({
          id: 'test-media-1',
          name: 'video1.mp4',
        }),
        createMockMediaItem({
          id: 'test-media-2',
          name: 'video2.mp4',
        }),
      ]

      const settings = {
        format: 'mp4' as const,
        quality: '1080p' as const,
        fps: 30,
        bitrate: 'high' as const,
        audioCodec: 'aac' as const,
        videoCodec: 'h264' as const,
      }

      const result = await exportVideo({
        tracks,
        mediaItems,
        settings,
        totalDuration: 10,
      })

      expect(result).toBeInstanceOf(Blob)
      expect(mockFFmpeg.writeFile).toHaveBeenCalledTimes(2) // Two media files
    })

    it('should cleanup files on error', async () => {
      const tracks = [
        createMockTimelineTrack({
          type: 'media',
          elements: [
            createMockTimelineElement({
              type: 'media',
              mediaId: 'test-media-1',
            }),
          ],
        }),
      ]

      const mediaItems = [
        createMockMediaItem({
          id: 'test-media-1',
        }),
      ]

      const settings = {
        format: 'mp4' as const,
        quality: '1080p' as const,
        fps: 30,
        bitrate: 'auto' as const,
        audioCodec: 'aac' as const,
        videoCodec: 'h264' as const,
      }

      // Make FFmpeg exec throw an error
      mockFFmpeg.exec.mockRejectedValueOnce(new Error('FFmpeg error'))

      await expect(
        exportVideo({
          tracks,
          mediaItems,
          settings,
          totalDuration: 5,
        })
      ).rejects.toThrow('FFmpeg error')

      // Verify cleanup was attempted
      expect(mockFFmpeg.deleteFile).toHaveBeenCalled()
    })

    it('should handle different video codecs', async () => {
      const tracks = [
        createMockTimelineTrack({
          type: 'media',
          elements: [
            createMockTimelineElement({
              type: 'media',
              mediaId: 'test-media-1',
            }),
          ],
        }),
      ]

      const mediaItems = [
        createMockMediaItem({
          id: 'test-media-1',
        }),
      ]

      const testCases = [
        { videoCodec: 'h264', expected: 'libx264' },
        { videoCodec: 'h265', expected: 'libx265' },
        { videoCodec: 'vp9', expected: 'libvpx-vp9' },
        { videoCodec: 'av1', expected: 'libaom-av1' },
      ]

      for (const testCase of testCases) {
        const settings = {
          format: 'mp4' as const,
          quality: '1080p' as const,
          fps: 30,
          bitrate: 'auto' as const,
          audioCodec: 'aac' as const,
          videoCodec: testCase.videoCodec as any,
        }

        await exportVideo({
          tracks,
          mediaItems,
          settings,
          totalDuration: 5,
        })

        // Check that the correct codec was used in the FFmpeg command
        const execCalls = mockFFmpeg.exec.mock.calls
        const lastCall = execCalls[execCalls.length - 1][0]
        expect(lastCall).toContain('-c:v')
        expect(lastCall).toContain(testCase.expected)
      }
    })
  })
})
