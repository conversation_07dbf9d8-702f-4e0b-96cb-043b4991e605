import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useTimelineStore } from '../timeline-store'
import { createMockTimelineTrack, createMockTimelineElement } from '@/test/utils'

// Mock the storage service
vi.mock('@/lib/storage/storage-service', () => ({
  storageService: {
    saveTimeline: vi.fn(),
    loadTimeline: vi.fn(),
  },
}))

// Mock other stores
vi.mock('../media-store', () => ({
  useMediaStore: {
    getState: () => ({
      mediaItems: [
        {
          id: 'test-media-1',
          name: 'Test Video',
          type: 'video',
          duration: 10,
        },
      ],
    }),
  },
}))

vi.mock('../editor-store', () => ({
  useEditorStore: {
    getState: () => ({
      setCanvasSizeFromAspectRatio: vi.fn(),
    }),
  },
}))

describe('Timeline Store', () => {
  beforeEach(() => {
    // Reset the store before each test
    useTimelineStore.setState({
      _tracks: [],
      selectedElements: [],
      dragState: null,
      history: [],
      redoStack: [],
    })
  })

  describe('Track Management', () => {
    it('should add a new track', () => {
      const { addTrack, tracks } = useTimelineStore.getState()
      
      const trackId = addTrack('media')
      
      expect(tracks).toHaveLength(1)
      expect(tracks[0]).toMatchObject({
        id: trackId,
        name: 'Media Track',
        type: 'media',
        elements: [],
        muted: false,
      })
    })

    it('should remove a track', () => {
      const { addTrack, removeTrack, tracks } = useTimelineStore.getState()
      
      const trackId = addTrack('media')
      expect(tracks).toHaveLength(1)
      
      removeTrack(trackId)
      expect(tracks).toHaveLength(0)
    })

    it('should insert track at specific index', () => {
      const { addTrack, insertTrackAt, tracks } = useTimelineStore.getState()
      
      // Add two tracks first
      addTrack('media')
      addTrack('audio')
      
      // Insert a text track at index 1
      const newTrackId = insertTrackAt('text', 1)
      
      expect(tracks).toHaveLength(3)
      expect(tracks[1]).toMatchObject({
        id: newTrackId,
        type: 'text',
        name: 'Text Track',
      })
    })
  })

  describe('Element Management', () => {
    it('should add element to track', () => {
      const { addTrack, addElementToTrack, tracks } = useTimelineStore.getState()
      
      const trackId = addTrack('media')
      const elementId = addElementToTrack(trackId, {
        type: 'media',
        mediaId: 'test-media-1',
        name: 'Test Element',
        duration: 5,
        startTime: 0,
      })
      
      expect(elementId).toBeDefined()
      expect(tracks[0].elements).toHaveLength(1)
      expect(tracks[0].elements[0]).toMatchObject({
        id: elementId,
        type: 'media',
        mediaId: 'test-media-1',
        name: 'Test Element',
        duration: 5,
        startTime: 0,
        trimStart: 0,
        trimEnd: 0,
      })
    })

    it('should not add element to non-existent track', () => {
      const { addElementToTrack } = useTimelineStore.getState()
      
      const elementId = addElementToTrack('non-existent', {
        type: 'media',
        mediaId: 'test-media-1',
        name: 'Test Element',
        duration: 5,
        startTime: 0,
      })
      
      expect(elementId).toBeUndefined()
    })

    it('should remove element from track', () => {
      const { addTrack, addElementToTrack, removeElementFromTrack, tracks } = useTimelineStore.getState()
      
      const trackId = addTrack('media')
      const elementId = addElementToTrack(trackId, {
        type: 'media',
        mediaId: 'test-media-1',
        name: 'Test Element',
        duration: 5,
        startTime: 0,
      })
      
      expect(tracks[0].elements).toHaveLength(1)
      
      removeElementFromTrack(trackId, elementId!)
      expect(tracks[0].elements).toHaveLength(0)
    })

    it('should move element between tracks', () => {
      const { addTrack, addElementToTrack, moveElementToTrack, tracks } = useTimelineStore.getState()
      
      const track1Id = addTrack('media')
      const track2Id = addTrack('media')
      
      const elementId = addElementToTrack(track1Id, {
        type: 'media',
        mediaId: 'test-media-1',
        name: 'Test Element',
        duration: 5,
        startTime: 0,
      })
      
      expect(tracks[0].elements).toHaveLength(1)
      expect(tracks[1].elements).toHaveLength(0)
      
      moveElementToTrack(track1Id, track2Id, elementId!)
      
      expect(tracks[0].elements).toHaveLength(0)
      expect(tracks[1].elements).toHaveLength(1)
    })
  })

  describe('Element Selection', () => {
    it('should select element', () => {
      const { addTrack, addElementToTrack, selectElement, selectedElements } = useTimelineStore.getState()
      
      const trackId = addTrack('media')
      const elementId = addElementToTrack(trackId, {
        type: 'media',
        mediaId: 'test-media-1',
        name: 'Test Element',
        duration: 5,
        startTime: 0,
      })
      
      selectElement(trackId, elementId!)
      
      expect(selectedElements).toEqual([{ trackId, elementId }])
    })

    it('should support multi-selection', () => {
      const { addTrack, addElementToTrack, selectElement, selectedElements } = useTimelineStore.getState()
      
      const trackId = addTrack('media')
      const element1Id = addElementToTrack(trackId, {
        type: 'media',
        mediaId: 'test-media-1',
        name: 'Test Element 1',
        duration: 5,
        startTime: 0,
      })
      const element2Id = addElementToTrack(trackId, {
        type: 'media',
        mediaId: 'test-media-1',
        name: 'Test Element 2',
        duration: 5,
        startTime: 6,
      })
      
      selectElement(trackId, element1Id!)
      selectElement(trackId, element2Id!, true) // multi-select
      
      expect(selectedElements).toHaveLength(2)
      expect(selectedElements).toContainEqual({ trackId, elementId: element1Id })
      expect(selectedElements).toContainEqual({ trackId, elementId: element2Id })
    })

    it('should clear selected elements', () => {
      const { addTrack, addElementToTrack, selectElement, clearSelectedElements, selectedElements } = useTimelineStore.getState()
      
      const trackId = addTrack('media')
      const elementId = addElementToTrack(trackId, {
        type: 'media',
        mediaId: 'test-media-1',
        name: 'Test Element',
        duration: 5,
        startTime: 0,
      })
      
      selectElement(trackId, elementId!)
      expect(selectedElements).toHaveLength(1)
      
      clearSelectedElements()
      expect(selectedElements).toHaveLength(0)
    })
  })

  describe('Element Updates', () => {
    it('should update element start time', () => {
      const { addTrack, addElementToTrack, updateElementStartTime, tracks } = useTimelineStore.getState()
      
      const trackId = addTrack('media')
      const elementId = addElementToTrack(trackId, {
        type: 'media',
        mediaId: 'test-media-1',
        name: 'Test Element',
        duration: 5,
        startTime: 0,
      })
      
      updateElementStartTime(trackId, elementId!, 10)
      
      expect(tracks[0].elements[0].startTime).toBe(10)
    })

    it('should update element trim', () => {
      const { addTrack, addElementToTrack, updateElementTrim, tracks } = useTimelineStore.getState()
      
      const trackId = addTrack('media')
      const elementId = addElementToTrack(trackId, {
        type: 'media',
        mediaId: 'test-media-1',
        name: 'Test Element',
        duration: 10,
        startTime: 0,
      })
      
      updateElementTrim(trackId, elementId!, 1, 2)
      
      expect(tracks[0].elements[0].trimStart).toBe(1)
      expect(tracks[0].elements[0].trimEnd).toBe(2)
    })

    it('should update element duration', () => {
      const { addTrack, addElementToTrack, updateElementDuration, tracks } = useTimelineStore.getState()
      
      const trackId = addTrack('media')
      const elementId = addElementToTrack(trackId, {
        type: 'media',
        mediaId: 'test-media-1',
        name: 'Test Element',
        duration: 5,
        startTime: 0,
      })
      
      updateElementDuration(trackId, elementId!, 8)
      
      expect(tracks[0].elements[0].duration).toBe(8)
    })
  })

  describe('Timeline Duration', () => {
    it('should calculate total duration correctly', () => {
      const { addTrack, addElementToTrack, getTotalDuration } = useTimelineStore.getState()
      
      const trackId = addTrack('media')
      
      // Add element from 0 to 5 seconds
      addElementToTrack(trackId, {
        type: 'media',
        mediaId: 'test-media-1',
        name: 'Element 1',
        duration: 5,
        startTime: 0,
      })
      
      // Add element from 10 to 15 seconds
      addElementToTrack(trackId, {
        type: 'media',
        mediaId: 'test-media-1',
        name: 'Element 2',
        duration: 5,
        startTime: 10,
      })
      
      expect(getTotalDuration()).toBe(15)
    })

    it('should handle empty timeline', () => {
      const { getTotalDuration } = useTimelineStore.getState()
      
      expect(getTotalDuration()).toBe(0)
    })
  })

  describe('History Management', () => {
    it('should push history when adding track', () => {
      const { addTrack, history } = useTimelineStore.getState()
      
      expect(history).toHaveLength(0)
      
      addTrack('media')
      
      expect(history).toHaveLength(1)
    })

    it('should support undo', () => {
      const { addTrack, undo, tracks } = useTimelineStore.getState()
      
      addTrack('media')
      expect(tracks).toHaveLength(1)
      
      undo()
      expect(tracks).toHaveLength(0)
    })

    it('should support redo', () => {
      const { addTrack, undo, redo, tracks } = useTimelineStore.getState()
      
      addTrack('media')
      expect(tracks).toHaveLength(1)
      
      undo()
      expect(tracks).toHaveLength(0)
      
      redo()
      expect(tracks).toHaveLength(1)
    })
  })
})
