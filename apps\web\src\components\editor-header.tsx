"use client";

import Link from "next/link";
import { useState } from "react";
import { Button } from "./ui/button";
import { ChevronLeft, Download } from "lucide-react";
import { useTimelineStore } from "@/stores/timeline-store";
import { useMediaStore } from "@/stores/media-store";
import { HeaderBase } from "./header-base";
import { formatTimeCode } from "@/lib/time";
import { useProjectStore } from "@/stores/project-store";
import { ExportDialog, ExportSettings } from "./editor/export-dialog";
import { exportVideo, ExportProgress } from "@/lib/export-utils";
import { toast } from "sonner";

export function EditorHeader() {
  const { getTotalDuration, tracks } = useTimelineStore();
  const { mediaItems } = useMediaStore();
  const { activeProject } = useProjectStore();

  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportStatus, setExportStatus] = useState<string>();

  const handleExport = () => {
    setExportDialogOpen(true);
  };

  const handleExportStart = async (settings: ExportSettings) => {
    if (!activeProject) {
      toast.error("No active project to export");
      return;
    }

    if (tracks.length === 0) {
      toast.error("No content to export. Add media to your timeline first.");
      return;
    }

    setIsExporting(true);
    setExportProgress(0);
    setExportStatus("Preparing export...");

    try {
      const totalDuration = getTotalDuration();

      const exportBlob = await exportVideo({
        tracks,
        mediaItems,
        settings,
        totalDuration,
        onProgress: (progress: ExportProgress) => {
          setExportProgress(progress.progress);
          setExportStatus(progress.message);
        }
      });

      // Download the exported video
      const url = URL.createObjectURL(exportBlob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `${activeProject.name}.${settings.format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success("Video exported successfully!");
      setExportDialogOpen(false);

    } catch (error) {
      console.error("Export failed:", error);
      toast.error("Export failed. Please try again.");
    } finally {
      setIsExporting(false);
      setExportProgress(0);
      setExportStatus(undefined);
    }
  };

  const leftContent = (
    <div className="flex items-center gap-2">
      <Link
        href="/projects"
        className="font-medium tracking-tight flex items-center gap-2 hover:opacity-80 transition-opacity"
      >
        <ChevronLeft className="h-4 w-4" />
        <span className="text-sm">{activeProject?.name}</span>
      </Link>
    </div>
  );

  const centerContent = (
    <div className="flex items-center gap-2 text-xs text-muted-foreground">
      <span>{formatTimeCode(getTotalDuration(), "HH:MM:SS:CS")}</span>
    </div>
  );

  const rightContent = (
    <nav className="flex items-center gap-2">
      <Button size="sm" variant="primary" onClick={handleExport}>
        <Download className="h-4 w-4" />
        <span className="text-sm">Export</span>
      </Button>
    </nav>
  );

  return (
    <>
      <HeaderBase
        leftContent={leftContent}
        centerContent={centerContent}
        rightContent={rightContent}
        className="bg-background border-b"
      />

      <ExportDialog
        open={exportDialogOpen}
        onOpenChange={setExportDialogOpen}
        onExport={handleExportStart}
        isExporting={isExporting}
        exportProgress={exportProgress}
        exportStatus={exportStatus}
      />
    </>
  );
}
