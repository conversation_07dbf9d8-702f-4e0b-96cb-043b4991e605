import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@/test/utils'
import { Timeline } from '../timeline'
import { createMockTimelineTrack, createMockTimelineElement, createMockMediaItem } from '@/test/utils'

// Mock the stores
const mockTimelineStore = {
  tracks: [],
  selectedElements: [],
  dragState: null,
  addTrack: vi.fn(),
  addElementToTrack: vi.fn(),
  removeElementFromTrack: vi.fn(),
  getTotalDuration: vi.fn(() => 10),
  clearSelectedElements: vi.fn(),
  setSelectedElements: vi.fn(),
  splitElement: vi.fn(),
  undo: vi.fn(),
  redo: vi.fn(),
}

const mockMediaStore = {
  mediaItems: [],
  addMediaItem: vi.fn(),
}

const mockProjectStore = {
  activeProject: { id: 'test-project', name: 'Test Project' },
}

const mockPlaybackStore = {
  currentTime: 0,
  duration: 10,
  isPlaying: false,
  speed: 1,
  seek: vi.fn(),
  setDuration: vi.fn(),
  toggle: vi.fn(),
  setSpeed: vi.fn(),
}

vi.mock('@/stores/timeline-store', () => ({
  useTimelineStore: vi.fn(() => mockTimelineStore),
}))

vi.mock('@/stores/media-store', () => ({
  useMediaStore: vi.fn(() => mockMediaStore),
}))

vi.mock('@/stores/project-store', () => ({
  useProjectStore: vi.fn(() => mockProjectStore),
}))

vi.mock('@/stores/playback-store', () => ({
  usePlaybackStore: vi.fn(() => mockPlaybackStore),
}))

// Mock child components
vi.mock('../timeline-track', () => ({
  TimelineTrack: ({ track }: any) => (
    <div data-testid={`timeline-track-${track.id}`}>
      Track: {track.name} ({track.type})
    </div>
  ),
}))

vi.mock('../timeline-ruler', () => ({
  TimelineRuler: () => <div data-testid="timeline-ruler">Timeline Ruler</div>,
}))

describe('Timeline Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset mock store state
    mockTimelineStore.tracks = []
    mockTimelineStore.selectedElements = []
    mockTimelineStore.dragState = null
    mockMediaStore.mediaItems = []
  })

  it('should render empty timeline', () => {
    render(<Timeline />)
    
    expect(screen.getByText('Drop media here to add to timeline')).toBeInTheDocument()
    expect(screen.getByTestId('timeline-ruler')).toBeInTheDocument()
  })

  it('should render tracks when they exist', () => {
    const mockTracks = [
      createMockTimelineTrack({
        id: 'track-1',
        name: 'Video Track',
        type: 'media',
      }),
      createMockTimelineTrack({
        id: 'track-2',
        name: 'Audio Track',
        type: 'audio',
      }),
    ]
    
    mockTimelineStore.tracks = mockTracks
    
    render(<Timeline />)
    
    expect(screen.getByTestId('timeline-track-track-1')).toBeInTheDocument()
    expect(screen.getByTestId('timeline-track-track-2')).toBeInTheDocument()
    expect(screen.getByText('Track: Video Track (media)')).toBeInTheDocument()
    expect(screen.getByText('Track: Audio Track (audio)')).toBeInTheDocument()
  })

  it('should handle drag over events', () => {
    render(<Timeline />)
    
    const timeline = screen.getByRole('main')
    
    fireEvent.dragOver(timeline, {
      dataTransfer: {
        types: ['application/json'],
      },
    })
    
    // Should prevent default to allow drop
    expect(timeline).toBeInTheDocument()
  })

  it('should handle drop events with media', async () => {
    const mockMediaItem = createMockMediaItem({
      id: 'media-1',
      name: 'test-video.mp4',
      type: 'video',
    })
    
    mockMediaStore.mediaItems = [mockMediaItem]
    
    render(<Timeline />)
    
    const timeline = screen.getByRole('main')
    
    fireEvent.drop(timeline, {
      dataTransfer: {
        getData: vi.fn().mockReturnValue(JSON.stringify({
          type: 'media',
          id: 'media-1',
        })),
      },
    })
    
    await waitFor(() => {
      expect(mockTimelineStore.addTrack).toHaveBeenCalledWith('media')
    })
  })

  it('should handle keyboard shortcuts', () => {
    render(<Timeline />)
    
    // Test space bar for play/pause
    fireEvent.keyDown(document, { key: ' ', code: 'Space' })
    expect(mockPlaybackStore.toggle).toHaveBeenCalled()
    
    // Test delete key
    mockTimelineStore.selectedElements = [{ trackId: 'track-1', elementId: 'element-1' }]
    fireEvent.keyDown(document, { key: 'Delete' })
    expect(mockTimelineStore.removeElementFromTrack).toHaveBeenCalledWith('track-1', 'element-1')
    
    // Test undo (Ctrl+Z)
    fireEvent.keyDown(document, { key: 'z', ctrlKey: true })
    expect(mockTimelineStore.undo).toHaveBeenCalled()
    
    // Test redo (Ctrl+Y)
    fireEvent.keyDown(document, { key: 'y', ctrlKey: true })
    expect(mockTimelineStore.redo).toHaveBeenCalled()
  })

  it('should handle element duplication', () => {
    const mockElement = createMockTimelineElement({
      id: 'element-1',
      name: 'Test Element',
      duration: 5,
      startTime: 0,
    })
    
    const mockTrack = createMockTimelineTrack({
      id: 'track-1',
      elements: [mockElement],
    })
    
    mockTimelineStore.tracks = [mockTrack]
    mockTimelineStore.selectedElements = [{ trackId: 'track-1', elementId: 'element-1' }]
    mockTimelineStore.addElementToTrack.mockReturnValue('new-element-id')
    
    render(<Timeline />)
    
    // Test Ctrl+D for duplication
    fireEvent.keyDown(document, { key: 'd', ctrlKey: true })
    
    expect(mockTimelineStore.addElementToTrack).toHaveBeenCalledWith(
      'track-1',
      expect.objectContaining({
        name: 'Test Element (copy)',
        startTime: 5.1, // Original end time + 0.1
      })
    )
    
    expect(mockTimelineStore.setSelectedElements).toHaveBeenCalledWith([
      { trackId: 'track-1', elementId: 'new-element-id' }
    ])
  })

  it('should handle marquee selection', () => {
    render(<Timeline />)
    
    const timeline = screen.getByRole('main')
    
    // Start marquee selection
    fireEvent.mouseDown(timeline, { clientX: 100, clientY: 100 })
    fireEvent.mouseMove(timeline, { clientX: 200, clientY: 200 })
    fireEvent.mouseUp(timeline)
    
    // Should call setSelectedElements (even if empty)
    expect(mockTimelineStore.setSelectedElements).toHaveBeenCalled()
  })

  it('should handle zoom controls', () => {
    render(<Timeline />)
    
    // Test zoom in (Ctrl + Plus)
    fireEvent.keyDown(document, { key: '+', ctrlKey: true })
    
    // Test zoom out (Ctrl + Minus)
    fireEvent.keyDown(document, { key: '-', ctrlKey: true })
    
    // Test zoom reset (Ctrl + 0)
    fireEvent.keyDown(document, { key: '0', ctrlKey: true })
    
    // These should not throw errors
    expect(timeline).toBeInTheDocument()
  })

  it('should display total duration', () => {
    mockTimelineStore.getTotalDuration.mockReturnValue(125.5)
    
    render(<Timeline />)
    
    // Should format duration as MM:SS
    expect(screen.getByText('02:05')).toBeInTheDocument()
  })

  it('should handle empty state with no media', () => {
    render(<Timeline />)
    
    expect(screen.getByText('Drop media here to add to timeline')).toBeInTheDocument()
    expect(screen.getByText('Add media files to your project to get started')).toBeInTheDocument()
  })

  it('should handle track addition buttons', () => {
    render(<Timeline />)
    
    // Find and click add track buttons
    const addVideoButton = screen.getByText('Add Video Track')
    const addAudioButton = screen.getByText('Add Audio Track')
    const addTextButton = screen.getByText('Add Text Track')
    
    fireEvent.click(addVideoButton)
    expect(mockTimelineStore.addTrack).toHaveBeenCalledWith('media')
    
    fireEvent.click(addAudioButton)
    expect(mockTimelineStore.addTrack).toHaveBeenCalledWith('audio')
    
    fireEvent.click(addTextButton)
    expect(mockTimelineStore.addTrack).toHaveBeenCalledWith('text')
  })

  it('should handle playhead seeking', () => {
    render(<Timeline />)
    
    const timeline = screen.getByRole('main')
    
    // Click on timeline to seek
    fireEvent.click(timeline, { clientX: 200 })
    
    // Should call seek function
    expect(mockPlaybackStore.seek).toHaveBeenCalled()
  })

  it('should handle speed controls', () => {
    render(<Timeline />)
    
    // Test speed shortcuts
    fireEvent.keyDown(document, { key: '1' })
    expect(mockPlaybackStore.setSpeed).toHaveBeenCalledWith(1)
    
    fireEvent.keyDown(document, { key: '2' })
    expect(mockPlaybackStore.setSpeed).toHaveBeenCalledWith(2)
    
    fireEvent.keyDown(document, { key: '0', key: '.' })
    expect(mockPlaybackStore.setSpeed).toHaveBeenCalledWith(0.5)
  })
})
