import React from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { vi } from 'vitest'
import { TimelineTrack, TimelineElement } from '@/types/timeline'
import { MediaItem } from '@/stores/media-store'
import { TProject } from '@/types/project'

// Custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>
}

const customRender = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

export * from '@testing-library/react'
export { customRender as render }

// Test data factories
export const createMockMediaItem = (overrides: Partial<MediaItem> = {}): MediaItem => ({
  id: 'test-media-' + Math.random().toString(36).substr(2, 9),
  name: 'Test Video.mp4',
  type: 'video',
  file: new File(['test'], 'test-video.mp4', { type: 'video/mp4' }),
  duration: 10,
  size: 1024 * 1024,
  createdAt: new Date(),
  thumbnail: 'data:image/jpeg;base64,test',
  ...overrides,
})

export const createMockTimelineElement = (overrides: Partial<TimelineElement> = {}): TimelineElement => ({
  id: 'test-element-' + Math.random().toString(36).substr(2, 9),
  type: 'media',
  mediaId: 'test-media-id',
  name: 'Test Element',
  startTime: 0,
  duration: 5,
  trimStart: 0,
  trimEnd: 0,
  ...overrides,
})

export const createMockTimelineTrack = (overrides: Partial<TimelineTrack> = {}): TimelineTrack => ({
  id: 'test-track-' + Math.random().toString(36).substr(2, 9),
  name: 'Test Track',
  type: 'media',
  elements: [],
  muted: false,
  ...overrides,
})

export const createMockProject = (overrides: Partial<TProject> = {}): TProject => ({
  id: 'test-project-' + Math.random().toString(36).substr(2, 9),
  name: 'Test Project',
  description: 'A test project',
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
})

// Mock store helpers
export const createMockTimelineStore = (overrides: any = {}) => ({
  tracks: [],
  selectedElements: [],
  dragState: null,
  addTrack: vi.fn(),
  addElementToTrack: vi.fn(),
  removeElementFromTrack: vi.fn(),
  selectElement: vi.fn(),
  clearSelectedElements: vi.fn(),
  setSelectedElements: vi.fn(),
  updateElementStartTime: vi.fn(),
  updateElementTrim: vi.fn(),
  getTotalDuration: vi.fn(() => 10),
  splitElement: vi.fn(),
  undo: vi.fn(),
  redo: vi.fn(),
  ...overrides,
})

export const createMockMediaStore = (overrides: any = {}) => ({
  mediaItems: [],
  addMediaItem: vi.fn(),
  removeMediaItem: vi.fn(),
  getMediaItem: vi.fn(),
  ...overrides,
})

export const createMockProjectStore = (overrides: any = {}) => ({
  activeProject: null,
  projects: [],
  loadProject: vi.fn(),
  createNewProject: vi.fn(),
  saveProject: vi.fn(),
  deleteProject: vi.fn(),
  ...overrides,
})

export const createMockPlaybackStore = (overrides: any = {}) => ({
  currentTime: 0,
  duration: 10,
  isPlaying: false,
  speed: 1,
  volume: 1,
  muted: false,
  play: vi.fn(),
  pause: vi.fn(),
  toggle: vi.fn(),
  seek: vi.fn(),
  setSpeed: vi.fn(),
  setVolume: vi.fn(),
  setMuted: vi.fn(),
  setDuration: vi.fn(),
  ...overrides,
})

// Helper to create a mock file with specific properties
export const createMockFile = (
  name: string = 'test.mp4',
  type: string = 'video/mp4',
  size: number = 1024 * 1024
): File => {
  const file = new File(['test content'], name, { type })
  Object.defineProperty(file, 'size', { value: size })
  return file
}

// Helper to create a mock video element
export const createMockVideoElement = () => {
  const video = document.createElement('video')
  Object.defineProperty(video, 'duration', { value: 10, writable: true })
  Object.defineProperty(video, 'videoWidth', { value: 1920, writable: true })
  Object.defineProperty(video, 'videoHeight', { value: 1080, writable: true })
  Object.defineProperty(video, 'currentTime', { value: 0, writable: true })
  Object.defineProperty(video, 'paused', { value: true, writable: true })
  return video
}

// Helper to create a mock audio element
export const createMockAudioElement = () => {
  const audio = document.createElement('audio')
  Object.defineProperty(audio, 'duration', { value: 10, writable: true })
  Object.defineProperty(audio, 'currentTime', { value: 0, writable: true })
  Object.defineProperty(audio, 'paused', { value: true, writable: true })
  return audio
}

// Helper to simulate drag and drop events
export const createDragEvent = (type: string, dataTransfer: any = {}) => {
  const event = new Event(type, { bubbles: true, cancelable: true }) as any
  event.dataTransfer = {
    getData: vi.fn(),
    setData: vi.fn(),
    clearData: vi.fn(),
    files: [],
    items: [],
    types: [],
    effectAllowed: 'all',
    dropEffect: 'none',
    ...dataTransfer,
  }
  return event
}

// Helper to wait for async operations
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// Helper to mock Zustand stores
export const mockZustandStore = (initialState: any) => {
  let state = initialState
  const listeners = new Set<() => void>()
  
  return {
    getState: () => state,
    setState: (partial: any) => {
      state = typeof partial === 'function' ? partial(state) : { ...state, ...partial }
      listeners.forEach(listener => listener())
    },
    subscribe: (listener: () => void) => {
      listeners.add(listener)
      return () => listeners.delete(listener)
    },
    destroy: () => listeners.clear(),
  }
}
