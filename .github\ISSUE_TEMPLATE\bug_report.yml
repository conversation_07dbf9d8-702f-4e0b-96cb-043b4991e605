name: Bug report
description: Create a report to help us improve
title: '[BUG] '
labels: bug
body:
  - type: input
    id: Platform
    attributes:
      label: Platform
      description: Please enter the platform on which you encountered the bug.
      placeholder: e.g. Windows 11, Ubuntu 14.04
    validations:
      required: true
  - type: input
    id: Browser
    attributes:
      label: Browser
      description:  Please enter the browser on which you encountered the bug.
      placeholder: e.g. Chrome 137, Firefox 137, Safari 17
    validations:
      required: true
  - type: textarea
    id: current-behavior
    attributes:
      label: Current Behavior
      description: A concise description of what you're experiencing.
    validations:
      required: true
  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected Behavior
      description: A concise description of what you expected to happen.
    validations:
      required: false
  - type: dropdown
    id: recurrence-probability
    attributes:
      label: Recurrence Probability
      description: How often does this bug occur?
      options:
        - Always
        - Usually
        - Sometimes
        - Seldom
      default: 0
    validations:
      required: true
  - type: textarea
    id: steps-to-reproduce
    attributes:
      label: Steps To Reproduce
      description: Steps to reproduce the behavior.
      placeholder: |
        1. Go to '...'
        2. Click on '....'
        3. Scroll down to '....'
        4. See error
    validations:
      required: true
  - type: textarea
    id: additional-context
    attributes:
      label: Anything else?
      description: |
        Links? References? Anything that will give us more context about the issue you are encountering!

        Tip: You can attach images or log files by clicking this area to highlight it and then dragging files in.
    validations:
      required: false