import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useProjectStore } from '../project-store'
import { createMockProject } from '@/test/utils'

// Mock the storage service
const mockStorageService = {
  saveProject: vi.fn(),
  loadProject: vi.fn(),
  deleteProject: vi.fn(),
  getAllProjects: vi.fn().mockResolvedValue([]),
}

vi.mock('@/lib/storage/storage-service', () => ({
  storageService: mockStorageService,
}))

describe('Project Store', () => {
  beforeEach(() => {
    // Reset the store before each test
    useProjectStore.setState({
      activeProject: null,
      projects: [],
    })
    vi.clearAllMocks()
  })

  describe('Project Creation', () => {
    it('should create a new project', async () => {
      const { createNewProject, activeProject } = useProjectStore.getState()
      
      mockStorageService.saveProject.mockResolvedValueOnce(undefined)
      
      await createNewProject('Test Project', 'Test description')
      
      const state = useProjectStore.getState()
      expect(state.activeProject).toBeTruthy()
      expect(state.activeProject?.name).toBe('Test Project')
      expect(state.activeProject?.description).toBe('Test description')
      expect(mockStorageService.saveProject).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Test Project',
          description: 'Test description',
        })
      )
    })

    it('should create project with default description', async () => {
      const { createNewProject } = useProjectStore.getState()
      
      mockStorageService.saveProject.mockResolvedValueOnce(undefined)
      
      await createNewProject('Test Project')
      
      const state = useProjectStore.getState()
      expect(state.activeProject?.description).toBe('')
    })

    it('should handle creation errors', async () => {
      const { createNewProject } = useProjectStore.getState()
      
      mockStorageService.saveProject.mockRejectedValueOnce(new Error('Save failed'))
      
      await expect(createNewProject('Test Project')).rejects.toThrow('Save failed')
      
      const state = useProjectStore.getState()
      expect(state.activeProject).toBeNull()
    })
  })

  describe('Project Loading', () => {
    it('should load an existing project', async () => {
      const mockProject = createMockProject({
        id: 'test-project-1',
        name: 'Loaded Project',
      })
      
      mockStorageService.loadProject.mockResolvedValueOnce(mockProject)
      
      const { loadProject } = useProjectStore.getState()
      await loadProject('test-project-1')
      
      const state = useProjectStore.getState()
      expect(state.activeProject).toEqual(mockProject)
      expect(mockStorageService.loadProject).toHaveBeenCalledWith('test-project-1')
    })

    it('should throw error when project not found', async () => {
      mockStorageService.loadProject.mockResolvedValueOnce(null)
      
      const { loadProject } = useProjectStore.getState()
      
      await expect(loadProject('non-existent')).rejects.toThrow('Project not found')
      
      const state = useProjectStore.getState()
      expect(state.activeProject).toBeNull()
    })

    it('should handle loading errors', async () => {
      mockStorageService.loadProject.mockRejectedValueOnce(new Error('Load failed'))
      
      const { loadProject } = useProjectStore.getState()
      
      await expect(loadProject('test-project-1')).rejects.toThrow('Load failed')
    })
  })

  describe('Project Saving', () => {
    it('should save the active project', async () => {
      const mockProject = createMockProject({
        id: 'test-project-1',
        name: 'Test Project',
      })
      
      useProjectStore.setState({ activeProject: mockProject })
      mockStorageService.saveProject.mockResolvedValueOnce(undefined)
      
      const { saveProject } = useProjectStore.getState()
      await saveProject()
      
      expect(mockStorageService.saveProject).toHaveBeenCalledWith(mockProject)
    })

    it('should throw error when no active project', async () => {
      const { saveProject } = useProjectStore.getState()
      
      await expect(saveProject()).rejects.toThrow('No active project to save')
    })

    it('should handle saving errors', async () => {
      const mockProject = createMockProject()
      useProjectStore.setState({ activeProject: mockProject })
      
      mockStorageService.saveProject.mockRejectedValueOnce(new Error('Save failed'))
      
      const { saveProject } = useProjectStore.getState()
      
      await expect(saveProject()).rejects.toThrow('Save failed')
    })
  })

  describe('Project Deletion', () => {
    it('should delete a project', async () => {
      const mockProject = createMockProject({
        id: 'test-project-1',
        name: 'Project to Delete',
      })
      
      useProjectStore.setState({
        projects: [mockProject],
        activeProject: mockProject,
      })
      
      mockStorageService.deleteProject.mockResolvedValueOnce(undefined)
      
      const { deleteProject } = useProjectStore.getState()
      await deleteProject('test-project-1')
      
      const state = useProjectStore.getState()
      expect(state.projects).toHaveLength(0)
      expect(state.activeProject).toBeNull()
      expect(mockStorageService.deleteProject).toHaveBeenCalledWith('test-project-1')
    })

    it('should not clear active project if deleting different project', async () => {
      const activeProject = createMockProject({
        id: 'active-project',
        name: 'Active Project',
      })
      
      const otherProject = createMockProject({
        id: 'other-project',
        name: 'Other Project',
      })
      
      useProjectStore.setState({
        projects: [activeProject, otherProject],
        activeProject: activeProject,
      })
      
      mockStorageService.deleteProject.mockResolvedValueOnce(undefined)
      
      const { deleteProject } = useProjectStore.getState()
      await deleteProject('other-project')
      
      const state = useProjectStore.getState()
      expect(state.projects).toHaveLength(1)
      expect(state.projects[0]).toEqual(activeProject)
      expect(state.activeProject).toEqual(activeProject)
    })

    it('should handle deletion errors', async () => {
      mockStorageService.deleteProject.mockRejectedValueOnce(new Error('Delete failed'))
      
      const { deleteProject } = useProjectStore.getState()
      
      await expect(deleteProject('test-project-1')).rejects.toThrow('Delete failed')
    })
  })

  describe('Project Listing', () => {
    it('should load all projects', async () => {
      const mockProjects = [
        createMockProject({ id: 'project-1', name: 'Project 1' }),
        createMockProject({ id: 'project-2', name: 'Project 2' }),
      ]
      
      mockStorageService.getAllProjects.mockResolvedValueOnce(mockProjects)
      
      const { loadAllProjects } = useProjectStore.getState()
      await loadAllProjects()
      
      const state = useProjectStore.getState()
      expect(state.projects).toEqual(mockProjects)
      expect(mockStorageService.getAllProjects).toHaveBeenCalled()
    })

    it('should handle loading all projects errors', async () => {
      mockStorageService.getAllProjects.mockRejectedValueOnce(new Error('Load all failed'))
      
      const { loadAllProjects } = useProjectStore.getState()
      
      await expect(loadAllProjects()).rejects.toThrow('Load all failed')
    })
  })

  describe('Project Updates', () => {
    it('should update active project', () => {
      const mockProject = createMockProject({
        id: 'test-project-1',
        name: 'Original Name',
      })
      
      useProjectStore.setState({ activeProject: mockProject })
      
      const { updateActiveProject } = useProjectStore.getState()
      updateActiveProject({ name: 'Updated Name' })
      
      const state = useProjectStore.getState()
      expect(state.activeProject?.name).toBe('Updated Name')
      expect(state.activeProject?.id).toBe('test-project-1') // Should preserve other fields
    })

    it('should not update when no active project', () => {
      const { updateActiveProject } = useProjectStore.getState()
      
      // Should not throw error
      updateActiveProject({ name: 'Updated Name' })
      
      const state = useProjectStore.getState()
      expect(state.activeProject).toBeNull()
    })
  })

  describe('Project State Management', () => {
    it('should clear active project', () => {
      const mockProject = createMockProject()
      useProjectStore.setState({ activeProject: mockProject })
      
      const { clearActiveProject } = useProjectStore.getState()
      clearActiveProject()
      
      const state = useProjectStore.getState()
      expect(state.activeProject).toBeNull()
    })

    it('should check if project has unsaved changes', () => {
      const mockProject = createMockProject({
        updatedAt: new Date('2023-01-01'),
      })
      
      useProjectStore.setState({ activeProject: mockProject })
      
      const { hasUnsavedChanges } = useProjectStore.getState()
      
      // Initially no unsaved changes
      expect(hasUnsavedChanges()).toBe(false)
      
      // Update the project
      const { updateActiveProject } = useProjectStore.getState()
      updateActiveProject({ name: 'Updated Name' })
      
      // Now should have unsaved changes (updatedAt is newer)
      expect(hasUnsavedChanges()).toBe(true)
    })
  })
})
