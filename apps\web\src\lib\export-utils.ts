import { FFmpeg } from "@ffmpeg/ffmpeg";
import { initFFmpeg } from "./ffmpeg-utils";
import { TimelineTrack, TimelineElement } from "@/types/timeline";
import { MediaItem } from "@/stores/media-store";
import { ExportSettings } from "@/components/editor/export-dialog";

export interface ExportProgress {
  phase: "preparing" | "processing" | "finalizing" | "complete";
  progress: number;
  message: string;
}

export interface ExportContext {
  tracks: TimelineTrack[];
  mediaItems: MediaItem[];
  settings: ExportSettings;
  totalDuration: number;
  onProgress?: (progress: ExportProgress) => void;
}

/**
 * Main export function that orchestrates the entire video export process
 */
export async function exportVideo(context: ExportContext): Promise<Blob> {
  const { tracks, mediaItems, settings, totalDuration, onProgress } = context;
  
  onProgress?.({
    phase: "preparing",
    progress: 0,
    message: "Initializing FFmpeg..."
  });

  const ffmpeg = await initFFmpeg();
  
  try {
    // Step 1: Prepare media files
    onProgress?.({
      phase: "preparing",
      progress: 10,
      message: "Preparing media files..."
    });
    
    await prepareMediaFiles(ffmpeg, tracks, mediaItems);
    
    // Step 2: Generate filter complex for video composition
    onProgress?.({
      phase: "preparing",
      progress: 30,
      message: "Building video composition..."
    });

    const { filterComplex, inputFiles } = generateFilterComplex(tracks, settings, totalDuration);

    // Step 3: Execute FFmpeg command
    onProgress?.({
      phase: "processing",
      progress: 40,
      message: "Processing video..."
    });

    const outputBlob = await processVideo(ffmpeg, filterComplex, inputFiles, settings, totalDuration, (progress) => {
      onProgress?.({
        phase: "processing",
        progress: 40 + (progress * 0.5), // 40% to 90%
        message: `Processing video... ${Math.round(progress)}%`
      });
    });
    
    onProgress?.({
      phase: "finalizing",
      progress: 95,
      message: "Finalizing export..."
    });
    
    // Cleanup
    await cleanupTempFiles(ffmpeg, tracks, mediaItems);
    
    onProgress?.({
      phase: "complete",
      progress: 100,
      message: "Export complete!"
    });
    
    return outputBlob;
    
  } catch (error) {
    // Cleanup on error
    await cleanupTempFiles(ffmpeg, tracks, mediaItems);
    throw error;
  }
}

/**
 * Prepare all media files for FFmpeg processing
 */
async function prepareMediaFiles(
  ffmpeg: FFmpeg,
  tracks: TimelineTrack[],
  mediaItems: MediaItem[]
): Promise<void> {
  const usedMediaIds = new Set<string>();
  
  // Collect all used media IDs from tracks
  tracks.forEach(track => {
    track.elements.forEach(element => {
      if (element.type === "media") {
        usedMediaIds.add(element.mediaId);
      }
    });
  });
  
  // Write media files to FFmpeg
  for (const mediaId of usedMediaIds) {
    const mediaItem = mediaItems.find(item => item.id === mediaId);
    if (mediaItem) {
      const fileName = `input_${mediaId}.${getFileExtension(mediaItem.file.name)}`;
      const fileData = new Uint8Array(await mediaItem.file.arrayBuffer());
      await ffmpeg.writeFile(fileName, fileData);
    }
  }
}

/**
 * Generate FFmpeg filter complex for video composition
 */
function generateFilterComplex(
  tracks: TimelineTrack[],
  settings: ExportSettings,
  totalDuration: number
): { filterComplex: string; inputFiles: string[] } {
  const filters: string[] = [];
  const inputFiles: string[] = [];
  const videoTracks = tracks.filter(track => track.type === "media" || track.type === "text");

  // Get output dimensions
  const { width, height } = getOutputDimensions(settings);

  // Collect all media elements and create input mapping
  const mediaElements: Array<{ element: TimelineElement; inputIndex: number; fileName: string }> = [];
  let inputIndex = 0;

  videoTracks.forEach(track => {
    track.elements.forEach(element => {
      if (element.type === "media") {
        const fileName = `input_${element.mediaId}.mp4`; // Simplified extension
        inputFiles.push(fileName);
        mediaElements.push({ element, inputIndex, fileName });
        inputIndex++;
      }
    });
  });

  // If no media elements, create a blank video
  if (mediaElements.length === 0) {
    filters.push(`color=c=black:s=${width}x${height}:d=${totalDuration}:r=${settings.fps}[video_out]`);
    filters.push(`anullsrc=channel_layout=stereo:sample_rate=48000:d=${totalDuration}[audio_out]`);
    return { filterComplex: filters.join(";"), inputFiles };
  }

  // Process each media element
  const processedSegments: string[] = [];

  mediaElements.forEach(({ element, inputIndex }, segmentIndex) => {
    const outputLabel = `segment_${segmentIndex}`;

    // Calculate timing
    const duration = element.duration - element.trimStart - element.trimEnd;
    const trimStart = element.trimStart;

    // Create video filter for this segment
    const videoFilter = [
      `[${inputIndex}:v]`,
      `trim=start=${trimStart}:duration=${duration}`,
      `setpts=PTS-STARTPTS`,
      `scale=${width}:${height}:force_original_aspect_ratio=decrease`,
      `pad=${width}:${height}:(ow-iw)/2:(oh-ih)/2:black`,
      `fps=${settings.fps}`,
      `[${outputLabel}_v]`
    ].join(",");

    // Create audio filter for this segment
    const audioFilter = [
      `[${inputIndex}:a]`,
      `atrim=start=${trimStart}:duration=${duration}`,
      `asetpts=PTS-STARTPTS`,
      `aformat=sample_fmts=fltp:sample_rates=48000:channel_layouts=stereo`,
      `[${outputLabel}_a]`
    ].join(",");

    filters.push(videoFilter);
    filters.push(audioFilter);
    processedSegments.push(`[${outputLabel}_v][${outputLabel}_a]`);
  });

  // Concatenate all segments
  if (processedSegments.length > 1) {
    const concatFilter = processedSegments.join("") +
      `concat=n=${processedSegments.length}:v=1:a=1[video_out][audio_out]`;
    filters.push(concatFilter);
  } else {
    // Single segment, just copy
    filters.push(`[segment_0_v]copy[video_out]`);
    filters.push(`[segment_0_a]copy[audio_out]`);
  }

  return { filterComplex: filters.join(";"), inputFiles };
}

/**
 * Execute the main FFmpeg processing command
 */
async function processVideo(
  ffmpeg: FFmpeg,
  filterComplex: string,
  inputFiles: string[],
  settings: ExportSettings,
  totalDuration: number,
  onProgress?: (progress: number) => void
): Promise<Blob> {
  const outputFileName = `output.${settings.format}`;

  // Set up progress tracking
  if (onProgress) {
    ffmpeg.on("progress", ({ progress }) => {
      onProgress(progress * 100);
    });
  }

  // Build FFmpeg command
  const command = buildFFmpegCommand(filterComplex, inputFiles, settings, outputFileName, totalDuration);

  // Execute command
  await ffmpeg.exec(command);

  // Read output file
  const data = await ffmpeg.readFile(outputFileName);
  const mimeType = getMimeType(settings.format);

  return new Blob([data], { type: mimeType });
}

/**
 * Build the complete FFmpeg command array
 */
function buildFFmpegCommand(
  filterComplex: string,
  inputFiles: string[],
  settings: ExportSettings,
  outputFileName: string,
  totalDuration: number
): string[] {
  const command: string[] = [];

  // Add input files
  inputFiles.forEach(file => {
    command.push("-i", file);
  });

  // Add filter complex if we have inputs
  if (inputFiles.length > 0) {
    command.push("-filter_complex", filterComplex);
    command.push("-map", "[video_out]", "-map", "[audio_out]");
  } else {
    // No inputs, use the filter complex directly
    command.push("-f", "lavfi", "-i", filterComplex);
  }

  // Video codec settings
  command.push("-c:v", getVideoCodec(settings.videoCodec));

  // Audio codec settings
  command.push("-c:a", getAudioCodec(settings.audioCodec));

  // Quality settings
  const bitrate = getBitrate(settings);
  if (bitrate) {
    command.push("-b:v", `${bitrate}k`);
  }

  // Frame rate
  command.push("-r", settings.fps.toString());

  // Duration
  command.push("-t", totalDuration.toString());

  // Output format specific settings
  if (settings.format === "mp4") {
    command.push("-movflags", "+faststart");
  }

  // Output file
  command.push(outputFileName);

  return command;
}

/**
 * Get output dimensions based on settings
 */
function getOutputDimensions(settings: ExportSettings): { width: number; height: number } {
  if (settings.quality === "custom") {
    return {
      width: settings.customWidth || 1920,
      height: settings.customHeight || 1080
    };
  }
  
  const presets = {
    "1080p": { width: 1920, height: 1080 },
    "720p": { width: 1280, height: 720 },
    "480p": { width: 854, height: 480 }
  };
  
  return presets[settings.quality] || presets["1080p"];
}

/**
 * Get bitrate based on settings
 */
function getBitrate(settings: ExportSettings): number | null {
  if (settings.bitrate === "custom") {
    return settings.customBitrate || 5000;
  }
  
  if (settings.bitrate === "auto") {
    return null; // Let FFmpeg decide
  }
  
  const { width, height } = getOutputDimensions(settings);
  const quality = width >= 1920 ? "1080p" : width >= 1280 ? "720p" : "480p";
  
  const presets = {
    high: { "1080p": 8000, "720p": 5000, "480p": 2500 },
    medium: { "1080p": 5000, "720p": 3000, "480p": 1500 },
    low: { "1080p": 3000, "720p": 2000, "480p": 1000 }
  };
  
  return presets[settings.bitrate as keyof typeof presets]?.[quality] || 5000;
}

/**
 * Helper functions
 */
function getFileExtension(fileName: string): string {
  return fileName.split('.').pop() || 'mp4';
}

function getVideoCodec(codec: string): string {
  const codecMap: Record<string, string> = {
    h264: "libx264",
    h265: "libx265", 
    vp9: "libvpx-vp9",
    av1: "libaom-av1"
  };
  return codecMap[codec] || "libx264";
}

function getAudioCodec(codec: string): string {
  const codecMap: Record<string, string> = {
    aac: "aac",
    mp3: "libmp3lame",
    opus: "libopus"
  };
  return codecMap[codec] || "aac";
}

function getMimeType(format: string): string {
  const mimeTypes: Record<string, string> = {
    mp4: "video/mp4",
    webm: "video/webm",
    mov: "video/quicktime"
  };
  return mimeTypes[format] || "video/mp4";
}

/**
 * Cleanup temporary files
 */
async function cleanupTempFiles(
  ffmpeg: FFmpeg,
  tracks: TimelineTrack[],
  mediaItems: MediaItem[]
): Promise<void> {
  try {
    // Clean up input files
    const usedMediaIds = new Set<string>();
    tracks.forEach(track => {
      track.elements.forEach(element => {
        if (element.type === "media") {
          usedMediaIds.add(element.mediaId);
        }
      });
    });
    
    for (const mediaId of usedMediaIds) {
      const mediaItem = mediaItems.find(item => item.id === mediaId);
      if (mediaItem) {
        const fileName = `input_${mediaId}.${getFileExtension(mediaItem.file.name)}`;
        try {
          await ffmpeg.deleteFile(fileName);
        } catch (e) {
          // Ignore cleanup errors
        }
      }
    }
    
    // Clean up output file
    try {
      await ffmpeg.deleteFile("output.mp4");
      await ffmpeg.deleteFile("output.webm");
      await ffmpeg.deleteFile("output.mov");
    } catch (e) {
      // Ignore cleanup errors
    }
  } catch (error) {
    console.warn("Error during cleanup:", error);
  }
}
