"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "../ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Label } from "../ui/label";
import { Progress } from "../ui/progress";
import { Download, X, Settings } from "lucide-react";
import { Separator } from "../ui/separator";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "../ui/collapsible";

export interface ExportSettings {
  format: "mp4" | "webm" | "mov";
  quality: "1080p" | "720p" | "480p" | "custom";
  customWidth?: number;
  customHeight?: number;
  fps: number;
  bitrate: "auto" | "high" | "medium" | "low" | "custom";
  customBitrate?: number;
  audioCodec: "aac" | "mp3" | "opus";
  videoCodec: "h264" | "h265" | "vp9" | "av1";
}

interface ExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onExport: (settings: ExportSettings) => void;
  isExporting: boolean;
  exportProgress: number;
  exportStatus?: string;
}

const QUALITY_PRESETS = {
  "1080p": { width: 1920, height: 1080 },
  "720p": { width: 1280, height: 720 },
  "480p": { width: 854, height: 480 },
};

const BITRATE_PRESETS = {
  high: { "1080p": 8000, "720p": 5000, "480p": 2500 },
  medium: { "1080p": 5000, "720p": 3000, "480p": 1500 },
  low: { "1080p": 3000, "720p": 2000, "480p": 1000 },
};

export function ExportDialog({
  open,
  onOpenChange,
  onExport,
  isExporting,
  exportProgress,
  exportStatus,
}: ExportDialogProps) {
  const [settings, setSettings] = useState<ExportSettings>({
    format: "mp4",
    quality: "1080p",
    fps: 30,
    bitrate: "auto",
    audioCodec: "aac",
    videoCodec: "h264",
  });
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleExport = () => {
    onExport(settings);
  };

  const updateSettings = (key: keyof ExportSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const getRecommendedCodecs = (format: string) => {
    switch (format) {
      case "mp4":
        return { video: ["h264", "h265"], audio: ["aac", "mp3"] };
      case "webm":
        return { video: ["vp9", "av1"], audio: ["opus"] };
      case "mov":
        return { video: ["h264", "h265"], audio: ["aac"] };
      default:
        return { video: ["h264"], audio: ["aac"] };
    }
  };

  const recommendedCodecs = getRecommendedCodecs(settings.format);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Video
          </DialogTitle>
          <DialogDescription>
            Configure your export settings and download your video.
          </DialogDescription>
        </DialogHeader>

        {isExporting ? (
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Exporting...</span>
                <span>{Math.round(exportProgress)}%</span>
              </div>
              <Progress value={exportProgress} className="w-full" />
            </div>
            {exportStatus && (
              <p className="text-sm text-muted-foreground">{exportStatus}</p>
            )}
          </div>
        ) : (
          <div className="space-y-6">
            {/* Basic Settings */}
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="format">Format</Label>
                  <Select
                    value={settings.format}
                    onValueChange={(value) => updateSettings("format", value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="mp4">MP4 (Recommended)</SelectItem>
                      <SelectItem value="webm">WebM</SelectItem>
                      <SelectItem value="mov">MOV</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="quality">Quality</Label>
                  <Select
                    value={settings.quality}
                    onValueChange={(value) => updateSettings("quality", value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1080p">1080p (1920×1080)</SelectItem>
                      <SelectItem value="720p">720p (1280×720)</SelectItem>
                      <SelectItem value="480p">480p (854×480)</SelectItem>
                      <SelectItem value="custom">Custom</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {settings.quality === "custom" && (
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="width">Width</Label>
                    <input
                      type="number"
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                      value={settings.customWidth || 1920}
                      onChange={(e) => updateSettings("customWidth", parseInt(e.target.value))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="height">Height</Label>
                    <input
                      type="number"
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                      value={settings.customHeight || 1080}
                      onChange={(e) => updateSettings("customHeight", parseInt(e.target.value))}
                    />
                  </div>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="fps">Frame Rate</Label>
                  <Select
                    value={settings.fps.toString()}
                    onValueChange={(value) => updateSettings("fps", parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="24">24 fps</SelectItem>
                      <SelectItem value="30">30 fps</SelectItem>
                      <SelectItem value="60">60 fps</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bitrate">Bitrate</Label>
                  <Select
                    value={settings.bitrate}
                    onValueChange={(value) => updateSettings("bitrate", value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="auto">Auto</SelectItem>
                      <SelectItem value="high">High Quality</SelectItem>
                      <SelectItem value="medium">Medium Quality</SelectItem>
                      <SelectItem value="low">Low Quality</SelectItem>
                      <SelectItem value="custom">Custom</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {settings.bitrate === "custom" && (
                <div className="space-y-2">
                  <Label htmlFor="customBitrate">Custom Bitrate (kbps)</Label>
                  <input
                    type="number"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    value={settings.customBitrate || 5000}
                    onChange={(e) => updateSettings("customBitrate", parseInt(e.target.value))}
                  />
                </div>
              )}
            </div>

            {/* Advanced Settings */}
            <Collapsible open={showAdvanced} onOpenChange={setShowAdvanced}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" className="flex items-center gap-2 p-0">
                  <Settings className="h-4 w-4" />
                  Advanced Settings
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-4 mt-4">
                <Separator />
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="videoCodec">Video Codec</Label>
                    <Select
                      value={settings.videoCodec}
                      onValueChange={(value) => updateSettings("videoCodec", value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {recommendedCodecs.video.map((codec) => (
                          <SelectItem key={codec} value={codec}>
                            {codec.toUpperCase()}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="audioCodec">Audio Codec</Label>
                    <Select
                      value={settings.audioCodec}
                      onValueChange={(value) => updateSettings("audioCodec", value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {recommendedCodecs.audio.map((codec) => (
                          <SelectItem key={codec} value={codec}>
                            {codec.toUpperCase()}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isExporting}>
            {isExporting ? "Exporting..." : "Cancel"}
          </Button>
          {!isExporting && (
            <Button onClick={handleExport}>
              <Download className="h-4 w-4 mr-2" />
              Export Video
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
